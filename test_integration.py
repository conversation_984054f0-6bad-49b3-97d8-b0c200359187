#!/usr/bin/env python3
"""
Integration test to verify the model configuration transparency feature.
"""

import requests
import json
import time

def test_api_endpoint():
    """Test the /api/config endpoint."""
    print("🧪 Testing Model Configuration API")
    print("=" * 50)
    
    try:
        # Test the API endpoint
        response = requests.get("http://localhost:2024/api/config", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint working correctly!")
            print(f"   - Model Provider: {data.get('model_provider')}")
            print(f"   - Query Generator: {data.get('query_generator_model')}")
            print(f"   - Reflection: {data.get('reflection_model')}")
            print(f"   - Answer: {data.get('answer_model')}")
            print(f"   - Provider Configured: {data.get('provider_configured')}")
            
            # Verify the data matches our .env configuration
            expected_provider = "openrouter"
            expected_models = "mistralai/devstral-small:free"
            
            if (data.get('model_provider') == expected_provider and
                data.get('query_generator_model') == expected_models and
                data.get('reflection_model') == expected_models and
                data.get('answer_model') == expected_models):
                print("✅ Configuration matches .env file!")
                return True
            else:
                print("⚠️  Configuration doesn't match expected values")
                return False
        else:
            print(f"❌ API endpoint failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error connecting to API: {e}")
        print("   Make sure the backend server is running on port 2024")
        return False

def test_frontend_access():
    """Test that the frontend is accessible."""
    print("\n🌐 Testing Frontend Access")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:2024/app", timeout=10)
        
        if response.status_code == 200:
            print("✅ Frontend is accessible!")
            print("   You can now open http://localhost:2024/app in your browser")
            return True
        else:
            print(f"❌ Frontend failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error accessing frontend: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Model Configuration Transparency Integration Test")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    api_test = test_api_endpoint()
    frontend_test = test_frontend_access()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   - API Configuration Endpoint: {'✅ PASS' if api_test else '❌ FAIL'}")
    print(f"   - Frontend Access: {'✅ PASS' if frontend_test else '❌ FAIL'}")
    
    if api_test and frontend_test:
        print("\n🎉 All tests passed! The model configuration transparency feature is working correctly.")
        print("\n📋 What's been implemented:")
        print("   1. ✅ Backend API endpoint (/api/config) exposes current model configuration")
        print("   2. ✅ Frontend dynamically fetches and displays actual model names")
        print("   3. ✅ UI shows provider information and configuration status")
        print("   4. ✅ Model configuration is read from environment variables")
        print("   5. ✅ Backward compatibility maintained")
        
        print("\n🔍 Next steps:")
        print("   - Open http://localhost:2024/app to see the new model configuration display")
        print("   - Try changing models in the .env file and restart to see updates")
        print("   - The frontend now shows transparency about which models are being used")
    else:
        print("\n❌ Some tests failed. Please check the server and try again.")

if __name__ == "__main__":
    main()
