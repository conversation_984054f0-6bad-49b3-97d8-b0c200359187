# Model Configuration Transparency Feature

## Overview

This feature provides transparency to users about which specific models are being used by each part of the LangGraph agent system. Instead of showing hardcoded values, the frontend now dynamically fetches and displays the actual model configurations from the backend environment variables.

## Implementation Details

### Backend Changes

#### 1. New API Endpoint: `/api/config`

**File:** `backend/src/agent/app.py`

Added a new REST API endpoint that exposes the current model configuration:

```python
@app.get("/api/config", response_model=ModelConfigResponse)
async def get_model_config():
    """Get the current model configuration."""
```

**Response Format:**
```json
{
  "model_provider": "openrouter",
  "query_generator_model": "mistralai/devstral-small:free",
  "reflection_model": "mistralai/devstral-small:free", 
  "answer_model": "mistralai/devstral-small:free",
  "provider_configured": true
}
```

#### 2. Configuration Reading

The endpoint reads configuration from:
- Environment variables (`MODEL_PROVIDER`, `QUERY_GENERATOR_MODEL`, etc.)
- Provider-specific model mappings
- Validates provider configuration status

### Frontend Changes

#### 1. New Hook: `useModelConfig`

**File:** `frontend/src/hooks/useModelConfig.ts`

A React hook that fetches model configuration from the backend API:

```typescript
export const useModelConfig = () => {
  const { config, loading, error } = useModelConfig();
  // Returns current model configuration
}
```

#### 2. New Component: `ModelConfigDisplay`

**File:** `frontend/src/components/ModelConfigDisplay.tsx`

A component that displays the current model configuration with:
- Provider information (Google Gemini / OpenRouter.ai)
- Configuration status indicator
- Detailed breakdown of models used for each task:
  - Query Generator Model
  - Reflection Model  
  - Answer Model

#### 3. Updated Components

**WelcomeScreen:** 
- Replaced hardcoded "Powered by Google Gemini" text
- Now shows dynamic provider information with configuration status

**InputForm:**
- Added information section showing actual backend models in use
- Provides transparency about which models handle each task

## Features

### ✅ Dynamic Configuration Display
- Shows actual models from environment variables
- Updates when backend configuration changes
- No hardcoded model names in frontend

### ✅ Provider Transparency  
- Displays current provider (Gemini/OpenRouter)
- Shows provider configuration status
- Appropriate icons and styling per provider

### ✅ Model Task Breakdown
- Query Generator: Model used for generating search queries
- Reflection: Model used for analyzing search results
- Answer: Model used for generating final answers

### ✅ Backward Compatibility
- Existing functionality unchanged
- Graceful fallback if API unavailable
- Maintains all existing code patterns

### ✅ Real-time Updates
- Configuration fetched on component mount
- Updates when backend restarts with new config
- No frontend rebuild required for model changes

## Usage

### For Users
1. Open the application at `http://localhost:2024/app`
2. See the model configuration display on the welcome screen
3. View detailed model breakdown in the input form
4. Configuration updates automatically when backend changes

### For Developers
1. Change models in `backend/.env` file:
   ```bash
   MODEL_PROVIDER=openrouter
   QUERY_GENERATOR_MODEL=anthropic/claude-3.5-sonnet
   REFLECTION_MODEL=openai/gpt-4o
   ANSWER_MODEL=anthropic/claude-3-opus
   ```
2. Restart backend server
3. Frontend automatically shows new configuration

## API Reference

### GET /api/config

Returns the current model configuration.

**Response:**
- `model_provider`: Current provider ("gemini" or "openrouter")
- `query_generator_model`: Model used for query generation
- `reflection_model`: Model used for reflection
- `answer_model`: Model used for answer generation  
- `provider_configured`: Boolean indicating if provider is properly configured

**Example:**
```bash
curl http://localhost:2024/api/config
```

## Testing

Run the test suite to verify implementation:

```bash
cd backend
uv run python test_final.py
```

This tests:
- API endpoint functionality
- Configuration reading from environment
- Model name resolution
- Provider validation

## Benefits

1. **Transparency**: Users know exactly which models are being used
2. **Flexibility**: Easy to change models without frontend changes  
3. **Debugging**: Clear visibility into model configuration
4. **Trust**: No hidden or hardcoded model selections
5. **Maintenance**: Single source of truth for model configuration
